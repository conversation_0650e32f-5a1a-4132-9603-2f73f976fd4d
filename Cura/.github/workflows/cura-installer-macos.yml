name: <PERSON><PERSON> Installer
run-name: ${{ inputs.cura_conan_version }} for Macos-${{ inputs.architecture }} by @${{ github.actor }}

on:
  workflow_call:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version'
        default: ''
        required: false
        type: string

      package_overrides:
        description: 'List of specific packages to be used (space-separated)'
        default: ''
        required: false
        type: string

      conan_args:
        description: 'Conan args'
        default: ''
        required: false
        type: string

      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean

      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean

      architecture:
        description: 'Architecture'
        required: true
        default: 'ARM64'
        type: string

      operating_system:
        description: 'OS'
        required: true
        default: 'self-hosted-ARM64'
        type: string

      private_data:
        required: false
        default: false
        type: boolean

permissions:
  contents: read

env:
  CODESIGN_IDENTITY: ${{ secrets.CODESIGN_IDENTITY }}
  MAC_NOTARIZE_USER: ${{ secrets.MAC_NOTARIZE_USER }}
  MAC_NOTARIZE_PASS: ${{ secrets.MAC_NOTARIZE_PASS }}
  MACOS_CERT_P12: ${{ secrets.MACOS_CERT_P12 }}
  MACOS_CERT_INSTALLER_P12: ${{ secrets.MACOS_CERT_INSTALLER_P12 }}
  MACOS_CERT_USER: ${{ secrets.MACOS_CERT_USER }}
  MACOS_CERT_PASSPHRASE: ${{ secrets.MACOS_CERT_PASSPHRASE }}

jobs:
  cura-installer-create:
    name: Build MacOS packages ${{ inputs.architecture }}
    runs-on: ${{ inputs.operating_system }}

    steps:
      - name: Setup the build environment
        id: setup-environment
        uses: ultimaker/cura-workflows/.github/actions/setup-build-environment@main
        with:
          conan_user: ${{ secrets.CONAN_USER }}
          conan_password: ${{ secrets.CONAN_PASS }}
          private_data: ${{ inputs.private_data }}
          install_system_dependencies: true
          repository_path: _cura_sources # we don't want the actual sources to interfere with the built conan package

      - name: Set packages overrides
        id: set-overrides
        uses: ultimaker/cura-workflows/.github/actions/set-package-overrides@main
        with:
          package_overrides: ${{ inputs.package_overrides }}
          profile: installer.jinja

      - name: Remove Macos keychain (Bash)
        run: security delete-keychain signing_temp.keychain || true

      - name: Configure Macos keychain Developer Cert(Bash)
        id: macos-keychain-developer-cert
        uses: apple-actions/import-codesign-certs@v3
        with:
          keychain-password: ${{ secrets.MACOS_KEYCHAIN_PASSWORD }}
          p12-file-base64: ${{ secrets.MACOS_CERT_P12 }}
          p12-password: ${{ secrets.MACOS_CERT_PASSPHRASE }}

      - name: Configure Macos keychain Installer Cert (Bash)
        id: macos-keychain-installer-cert
        uses: apple-actions/import-codesign-certs@v3
        with:
          keychain-password: ${{ secrets.MACOS_KEYCHAIN_PASSWORD }}
          create-keychain: false # keychain is created in previous use of action.
          p12-file-base64: ${{ secrets.MACOS_CERT_INSTALLER_P12 }}
          p12-password: ${{ secrets.MACOS_CERT_PASSPHRASE }}

      - name: Unlock Macos keychain (Bash)
        run: security unlock -p "${{  steps.macos-keychain-developer-cert.outputs.keychain-password }}" signing_temp.keychain

      - name: Gather/build the packages
        run: conan install --requires "${{ inputs.cura_conan_version != '' && inputs.cura_conan_version || format('cura/{0}@ultimaker/testing', steps.setup-environment.outputs.package_version) }}" ${{ inputs.conan_args }} --build=missing --update -of cura_inst --deployer-package="*" --profile ${{ steps.set-overrides.outputs.profile }} -c user.sentry:token="${{ secrets.CURAENGINE_SENTRY_TOKEN }}" ${{ inputs.enterprise && '-o "cura/*:enterprise=True"' || '' }} ${{ inputs.staging && '-o "cura/*:staging=True"' || '' }} ${{ inputs.private_data && '-o "cura/*:internal=True"' || '' }}

      - name: Create the Cura distribution with pyinstaller
        id: prepare-distribution
        run: |
          source cura_inst/conanrun.sh
          python -m venv cura_installer_venv
          source cura_installer_venv/bin/activate
          ls cura_inst/packaging/pip_requirements_{core,installer}_*.txt | xargs -I {} pip install -r {}

          python Cura-workflows/runner_scripts/prepare_installer.py --os ${{ runner.os }} --architecture ${{ inputs.architecture }} ${{ inputs.enterprise && '--enterprise' || '' }} ${{ inputs.private_data && '--internal' || '' }} --summary-output "$GITHUB_STEP_SUMMARY" --variables-output "$GITHUB_OUTPUT"

          plutil -convert xml1 cura_inst/packaging/MacOS/cura.entitlements
          pyinstaller ./cura_inst/UltiMaker-Cura.spec

      - name: Restore the pynavlib path
        run: |
          install_name_tool -change @rpath/3DconnexionNavlib /Library/Frameworks/3DconnexionNavlib.framework/3DconnexionNavlib "dist/UltiMaker Cura.app/Contents/Frameworks/pynavlib/_pynavlib.cpython-312-darwin.so"
          codesign -f -s "$CODESIGN_IDENTITY" "dist/UltiMaker Cura.app/Contents/Frameworks/pynavlib/_pynavlib.cpython-312-darwin.so"

      - name: Create the Macos dmg (Bash)
        run: python ../cura_inst/packaging/MacOS/build_macos.py --source_path ../cura_inst --dist_path . --cura_conan_version "${{ steps.prepare-distribution.outputs.CURA_VERSION }}" --filename "${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}" --build_dmg --build_pkg --app_name "${{ steps.prepare-distribution.outputs.CURA_APP_NAME }}"
        working-directory: dist

      - name: Upload the dmg
        uses: actions/upload-artifact@v4.3.3
        with:
          name: ${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}-dmg
          path: |
            dist/${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}.dmg
          retention-days: 5

      - name: Upload the pkg
        uses: actions/upload-artifact@v4.3.3
        with:
          name: ${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}-pkg
          path: |
            dist/${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}.pkg
          retention-days: 5

      - name: Upload the built Package(s)
        if: ${{ always() }}
        uses: ultimaker/cura-workflows/.github/actions/upload-conan-package@main
        continue-on-error: true

      - name: Clean local cache
        if: ${{ always() && startsWith(inputs.operating_system, 'self-hosted') }}
        run: conan remove '*' --lru=1w -c

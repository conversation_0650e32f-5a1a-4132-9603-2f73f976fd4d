name: Windows Installer
run-name: ${{ inputs.cura_conan_version }} for Windows-X64 by @${{ github.actor }}

on:
  workflow_call:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version'
        default: ''
        required: false
        type: string

      package_overrides:
        description: 'List of specific packages to be used (space-separated)'
        default: ''
        required: false
        type: string

      conan_args:
        description: 'Conan args'
        default: ''
        required: false
        type: string

      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean

      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean

      operating_system:
        description: 'OS'
        required: true
        default: 'windows-2022'
        type: string

      private_data:
        required: false
        default: false
        type: boolean

permissions:
  contents: read

env:
  WIN_CERT_INSTALLER_CER: ${{ secrets.WIN_CERT_INSTALLER_CER }}
  WIN_CERT_INSTALLER_CER_PASS: ${{ secrets.WIN_CERT_INSTALLER_CER_PASS }}
  SENTRY_TOKEN: ${{ secrets.CURAENGINE_SENTRY_TOKEN }}
  # win-cert-installer is probably not nescesary at all: went out the moment we started using signkey again on our own runner
  # sentry debug symbols seem to only be uploaded for linux?


# NOTE: Windows needs bash on Path, so we can use it in the (build env setup) scripts -- you should be able to use the bash that comes in the git/../bin folder.


jobs:
  cura-installer-create:
    name: Build Windows installers
    runs-on: ${{ inputs.operating_system }}

    steps:
      - name: Setup the build environment
        id: setup-environment
        uses: wsd07/Cura/.github/actions/setup-build-environment@main
        with:
          install_system_dependencies: true
          repository_path: _cura_sources # we don't want the actual sources to interfere with the built conan package
          python_set_env_vars: false

      - name: Set packages overrides
        id: set-overrides
        uses: wsd07/Cura/.github/actions/set-package-overrides@main
        with:
          package_overrides: ${{ inputs.package_overrides }}
          profile: installer.jinja

      - name: Gather/build the packages
        run: |
            $pydir = type pydir.txt
            $env:PATH = "$pydir;$pydir\Scripts;" + $env:PATH
            Write-Host "Using Python from: $pydir"
            Write-Host "Python version:"
            & "$pydir\python.exe" --version
            Write-Host "Conan version:"
            & "$pydir\python.exe" -m pip show conan
            cd _cura_sources
            Write-Host "Running conan install..."
            conan install . --build=missing --update -g VirtualPythonEnv -of ../cura_inst
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Conan install failed with exit code $LASTEXITCODE"
                exit $LASTEXITCODE
            }
            cd ..
            Write-Host "Checking generated files:"
            if (Test-Path "cura_inst") {
                Write-Host "cura_inst directory exists"
                Write-Host "Contents of cura_inst:"
                Get-ChildItem "cura_inst" -Recurse | Select-Object FullName, Length | Format-Table -AutoSize
                Write-Host "Looking for specific files:"
                Write-Host "conanrun.bat exists: $(Test-Path 'cura_inst\conanrun.bat')"
                Write-Host "packaging directory exists: $(Test-Path 'cura_inst\packaging')"
                if (Test-Path "cura_inst\packaging") {
                    Write-Host "Contents of packaging directory:"
                    Get-ChildItem "cura_inst\packaging" -Recurse | Select-Object FullName | Format-Table -AutoSize
                }
            } else {
                Write-Error "cura_inst directory not found!"
                exit 1
            }

      - name: Create the Cura distribution with pyinstaller
        id: prepare-distribution
        shell: cmd
        run: |
            echo "=== Debugging cura_inst directory ==="
            if exist "cura_inst" (
                echo "cura_inst directory exists"
                dir cura_inst
                echo "Checking for conanrun.bat in build\generators:"
                if exist "cura_inst\build\generators\conanrun.bat" (
                    echo "conanrun.bat found in build\generators"
                    type cura_inst\build\generators\conanrun.bat
                ) else (
                    echo "ERROR: conanrun.bat not found in build\generators!"
                    echo "Available files in cura_inst:"
                    dir cura_inst /s
                    exit /b 1
                )
            ) else (
                echo "ERROR: cura_inst directory not found!"
                exit /b 1
            )

            echo "=== Calling virtual environment setup ==="
            echo "First calling conanrun.bat:"
            call cura_inst\build\generators\conanrun.bat
            if %ERRORLEVEL% neq 0 (
                echo "conanrun.bat failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "Then calling virtual_python_env.bat:"
            call cura_inst\build\generators\virtual_python_env.bat
            if %ERRORLEVEL% neq 0 (
                echo "virtual_python_env.bat failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "=== Checking Python environment after Conan setup ==="
            python --version
            pip --version
            echo "Python path: %PYTHON%"
            echo "Current PATH: %PATH%"
            echo "PYTHONPATH: %PYTHONPATH%"

            echo "=== Checking if cura module is available ==="
            python -c "import sys; print('Python sys.path:'); [print(f'  {p}') for p in sys.path]"
            python -c "try: import cura; print('✓ cura module found'); except ImportError as e: print(f'✗ cura module not found: {e}')"

            echo "=== Looking for cura module in file system ==="
            if exist "cura_inst\build\generators\cura_venv" (
                echo "Checking cura_venv directory:"
                dir cura_inst\build\generators\cura_venv\Lib\site-packages | findstr /i cura
            )
            if exist "_cura_sources" (
                echo "Checking _cura_sources directory:"
                dir _cura_sources | findstr /i cura
            )

            echo "=== Adding _cura_sources to PYTHONPATH ==="
            set PYTHONPATH=%CD%\_cura_sources;%PYTHONPATH%
            echo "Updated PYTHONPATH: %PYTHONPATH%"

            echo "=== Testing cura import again ==="
            python -c "try: import cura; print('✓ cura module found after PYTHONPATH update'); except ImportError as e: print(f'✗ cura module still not found: {e}')"

            echo "=== Checking for pip requirements files ==="
            if exist "cura_inst\build\generators" (
                echo "generators directory exists"
                dir cura_inst\build\generators
            ) else (
                echo "ERROR: generators directory not found!"
                echo "Contents of cura_inst:"
                dir cura_inst /s
                exit /b 1
            )

            echo "=== Installing additional requirements in Conan environment ==="
            if exist "cura_inst\build\generators\pip_requirements_installer_basic.txt" (
                echo "Installing installer requirements (pyinstaller etc.)..."
                python -m pip install -r cura_inst\build\generators\pip_requirements_installer_basic.txt --no-warn-script-location
            ) else (
                echo "WARNING: pip_requirements_installer_basic.txt not found"
            )

            echo "=== Verifying cura module after pip installs ==="
            python -c "try: import cura; print('✓ cura module still available'); except ImportError as e: print(f'✗ cura module lost: {e}')"

            echo "=== Running prepare_installer.py ==="
            python Cura-workflows\runner_scripts\prepare_installer.py --os ${{ runner.os }} --architecture X64 ${{ inputs.enterprise && '--enterprise' || '' }} ${{ inputs.private_data && '--internal' || '' }} --summary-output %GITHUB_STEP_SUMMARY% --variables-output %GITHUB_OUTPUT%
            if %ERRORLEVEL% neq 0 (
                echo "prepare_installer.py failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "=== Checking for pyinstaller spec file ==="
            if exist "cura_inst\UltiMaker-Cura.spec" (
                echo "UltiMaker-Cura.spec found, running pyinstaller..."
                pyinstaller cura_inst\UltiMaker-Cura.spec
            ) else (
                echo "ERROR: UltiMaker-Cura.spec not found"
                echo "Contents of cura_inst:"
                dir cura_inst
                exit /b 1
            )

      - name: Copy precompiled CuraEngine.exe
        run: |
            Write-Host "=== Checking for precompiled CuraEngine.exe ==="
            Write-Host "Current directory: $(Get-Location)"
            Write-Host "Contents of current directory:"
            Get-ChildItem . | Select-Object Name, Length | Format-Table -AutoSize
            Write-Host "Contents of _cura_sources:"
            if (Test-Path "_cura_sources") {
                Get-ChildItem "_cura_sources" | Select-Object Name, Length | Format-Table -AutoSize
            } else {
                Write-Host "_cura_sources directory not found"
            }
            Write-Host "Contents of dist:"
            if (Test-Path "dist") {
                Get-ChildItem "dist" -Recurse | Select-Object FullName | Format-Table -AutoSize
            } else {
                Write-Host "dist directory not found"
            }

            if (Test-Path "_cura_sources\CuraEngine.exe") {
                Write-Host "Found precompiled CuraEngine.exe, copying to distribution"
                if (Test-Path "dist\UltiMaker-Cura") {
                    Copy-Item "_cura_sources\CuraEngine.exe" "dist\UltiMaker-Cura\CuraEngine.exe" -Force
                    Write-Host "CuraEngine.exe copied successfully to dist\UltiMaker-Cura\"
                } else {
                    Write-Error "dist\UltiMaker-Cura directory not found!"
                    exit 1
                }
            } else {
                Write-Error "CuraEngine.exe not found in _cura_sources directory. Please place the precompiled CuraEngine.exe file in the Cura project root."
                exit 1
            }

      # - name: Sign the internal executables (skipped for personal development)
        # working-directory: dist/UltiMaker-Cura
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "CuraEngine.exe"
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "UltiMaker-Cura.exe"
        # timeout-minutes: 2

      - name: Workaround (need exact version of msvc redistributables)
        run: |
          $MSDIR="C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Redist/MSVC"
          if ( (dir -Exclude "v*" $MSDIR).Count -ne 1 ) { throw "(!!!) MULTIPLE MSVC VERSIONS IN '$MSDIR' -- NEED EXACTLY 1 TO SELECT A REDISTRIBUTABLE (!!!)" }
          $MSDIR_R=(dir -Exclude "v*" $MSDIR)[0].FullName
          $MSDIR_DLLS=(dir $MSDIR_R/x64/Microsoft.VC*.CRT)[0].FullName
          copy $MSDIR_DLLS/concrt140.dll dist/UltiMaker-Cura/.
          copy $MSDIR_DLLS/msvcp140.dll dist/UltiMaker-Cura/.
          copy $MSDIR_DLLS/msvcp140_1.dll dist/UltiMaker-Cura/.
          copy $MSDIR_DLLS/msvcp140_2.dll dist/UltiMaker-Cura/.
          copy $MSDIR_DLLS/vcruntime140.dll dist/UltiMaker-Cura/.
          copy $MSDIR_DLLS/vcruntime140_1.dll dist/UltiMaker-Cura/.

      - name: Workaround (some libs linking against python3 instead of python312)
        run: |
          copy Cura-workflows/python_dll_workaround/* dist/UltiMaker-Cura/.

      - name: Yet another step deleting unwanted additional Qt files and folders
        working-directory: dist/UltiMaker-Cura
        run: |
         Remove-Item .\* -Include "*assimp*" -Recurse -Force
         Remove-Item .\* -Include "*qt6charts*" -Recurse -Force
         Remove-Item .\* -Include "*qt6coap*" -Recurse -Force
         Remove-Item .\* -Include "*qt6datavis*" -Recurse -Force
         Remove-Item .\* -Include "*qt6labsani*" -Recurse -Force
         Remove-Item .\* -Include "*qt6mqtt*" -Recurse -Force
         Remove-Item .\* -Include "*qt6networkauth*" -Recurse -Force
         Remove-Item .\* -Include "*quick3d*" -Recurse -Force
         Remove-Item .\* -Include "*qt6timeline*" -Recurse -Force
         Remove-Item .\* -Include "*qt6virtualkey*" -Recurse -Force
         Remove-Item .\* -Include "*waylandcomp*" -Recurse -Force
         Remove-Item .\* -Include "*qt5compat*" -Recurse -Force

      - name: Create the Windows exe installer (Powershell)
        run: |
          $pydir = type ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\NSIS\create_windows_installer.py --source_path ../cura_inst --dist_path . --filename "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # - name: Sign the Windows exe installer (skipped for personal development)
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe"
        # working-directory: dist
        # timeout-minutes: 2

      - name: Upload the installer exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-exe
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe
          retention-days: 5

      - name: Create the Windows msi installer (Powershell)
        run: |
          $pydir = type ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\msi\create_windows_msi.py --source_path ..\cura_inst --dist_path .\UltiMaker-Cura --filename "${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}.msi" --name "${{ steps.prepare-distribution.outputs.CURA_APP_NAME }}" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # - name: Sign the Windows msi installer (skipped for personal development)
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi"
        # working-directory: dist
        # timeout-minutes: 2

      - name: Upload the installer msi
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-msi
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi
          retention-days: 5

      - name: Upload the application exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: UltiMaker-Cura.exe
          path: dist/UltiMaker-Cura/UltiMaker-Cura.exe
          retention-days: 5

      - name: Upload the engine exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: CuraEngine.exe
          path: dist/UltiMaker-Cura/CuraEngine.exe
          retention-days: 5

      - name: Upload the built Package(s)
        if: ${{ always() }}
        uses: ultimaker/cura-workflows/.github/actions/upload-conan-package@main
        continue-on-error: true

      - name: Clean local cache
        if: ${{ always() && startsWith(inputs.operating_system, 'self-hosted') }}
        run: |
            $pydir = type pydir.txt
            $env:PATH = "$pydir;$pydir\Scripts;" + $env:PATH
            conan remove '*' --lru=1w -c

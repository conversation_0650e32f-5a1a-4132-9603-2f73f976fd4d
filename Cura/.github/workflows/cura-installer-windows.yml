# =============================================================================
# Cura Windows安装程序构建工作流
# =============================================================================
# 功能：为Windows平台构建Cura的EXE和MSI安装程序
# 触发方式：被其他workflow调用（workflow_call）
# 输出：Windows安装程序文件（.exe和.msi格式）
# =============================================================================

name: Windows Installer
# 运行时显示的名称，包含版本信息和触发用户
run-name: ${{ inputs.cura_conan_version }} for Windows-X64 by @${{ github.actor }}

# 工作流触发条件：只能被其他工作流调用
on:
  workflow_call:
    inputs:
      # Cura的Conan版本号（可选）
      cura_conan_version:
        description: 'Cura Conan Version'
        default: ''
        required: false
        type: string

      # 特定包覆盖列表（空格分隔，可选）
      package_overrides:
        description: 'List of specific packages to be used (space-separated)'
        default: ''
        required: false
        type: string

      # Conan额外参数（可选）
      conan_args:
        description: 'Conan args'
        default: ''
        required: false
        type: string

      # 是否构建企业版（必需，默认false）
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean

      # 是否使用staging API（必需，默认false）
      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean

      # 操作系统版本（必需，默认windows-2022）
      operating_system:
        description: 'OS'
        required: true
        default: 'windows-2022'
        type: string

      # 是否包含私有数据（可选，默认false）
      private_data:
        required: false
        default: false
        type: boolean

# 工作流权限：只读访问仓库内容
permissions:
  contents: read

# =============================================================================
# 环境变量配置
# =============================================================================
env:
  # Windows代码签名证书（来自GitHub Secrets）
  WIN_CERT_INSTALLER_CER: ${{ secrets.WIN_CERT_INSTALLER_CER }}
  WIN_CERT_INSTALLER_CER_PASS: ${{ secrets.WIN_CERT_INSTALLER_CER_PASS }}
  # Sentry错误追踪令牌
  SENTRY_TOKEN: ${{ secrets.CURAENGINE_SENTRY_TOKEN }}
  # 注意：win-cert-installer可能不再必要，因为我们在自己的runner上重新使用signkey
  # sentry调试符号似乎只在Linux上传？

# =============================================================================
# 重要说明
# =============================================================================
# Windows环境需要bash在PATH中，以便在构建环境设置脚本中使用
# 可以使用git安装目录下的bash：git/../bin/bash

# =============================================================================
# 构建作业定义
# =============================================================================
jobs:
  cura-installer-create:
    name: Build Windows installers  # 作业显示名称
    runs-on: ${{ inputs.operating_system }}  # 运行环境（通常是windows-2022）

    # =========================================================================
    # 构建步骤序列
    # =========================================================================
    steps:
      # 步骤1：设置构建环境
      # 功能：安装Python、Conan、系统依赖，检出源代码
      - name: Setup the build environment
        id: setup-environment
        uses: wsd07/Cura/.github/actions/setup-build-environment@main
        with:
          install_system_dependencies: true  # 安装系统级依赖
          repository_path: _cura_sources     # 源码路径（避免与构建的conan包冲突）
          python_set_env_vars: false         # 不设置Python环境变量

      # 步骤2：设置包覆盖配置
      # 功能：配置特定的Conan包版本覆盖
      - name: Set packages overrides
        id: set-overrides
        uses: wsd07/Cura/.github/actions/set-package-overrides@main
        with:
          package_overrides: ${{ inputs.package_overrides }}  # 包覆盖列表
          profile: installer.jinja                            # 使用的配置文件模板

      # 步骤3：收集/构建依赖包
      # 功能：使用Conan安装所有依赖包，生成虚拟Python环境
      - name: Gather/build the packages
        run: |
            # 读取Python目录路径并设置环境变量
            $pydir = type pydir.txt
            $env:PATH = "$pydir;$pydir\Scripts;" + $env:PATH

            # 显示Python和Conan版本信息（用于调试）
            Write-Host "Using Python from: $pydir"
            Write-Host "Python version:"
            & "$pydir\python.exe" --version
            Write-Host "Conan version:"
            & "$pydir\python.exe" -m pip show conan

            # 进入源码目录并执行Conan安装
            cd _cura_sources
            Write-Host "Running conan install with deployer-package..."
            # conan install参数说明：
            # --build=missing: 构建缺失的包
            # --update: 更新包到最新版本
            # -g VirtualPythonEnv: 生成虚拟Python环境
            # -of ../cura_inst: 输出到cura_inst目录
            # --deployer-package=*: 部署所有包
            conan install . --build=missing --update -g VirtualPythonEnv -of ../cura_inst --deployer-package=*

            # 检查Conan安装是否成功
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Conan install failed with exit code $LASTEXITCODE"
                exit $LASTEXITCODE
            }

            # 返回上级目录并验证生成的文件
            cd ..
            Write-Host "Checking generated files:"
            if (Test-Path "cura_inst") {
                Write-Host "cura_inst directory exists"
                Write-Host "Contents of cura_inst:"
                Get-ChildItem "cura_inst" -Recurse | Select-Object FullName, Length | Format-Table -AutoSize
                Write-Host "Looking for specific files:"
                Write-Host "conanrun.bat exists: $(Test-Path 'cura_inst\conanrun.bat')"
                Write-Host "packaging directory exists: $(Test-Path 'cura_inst\packaging')"
                if (Test-Path "cura_inst\packaging") {
                    Write-Host "Contents of packaging directory:"
                    Get-ChildItem "cura_inst\packaging" -Recurse | Select-Object FullName | Format-Table -AutoSize
                }
            } else {
                Write-Error "cura_inst directory not found!"
                exit 1
            }

      # 步骤4：使用PyInstaller创建Cura分发包
      # 功能：激活虚拟环境，运行PyInstaller生成独立可执行文件
      - name: Create the Cura distribution with pyinstaller
        id: prepare-distribution
        shell: cmd
        run: |
            echo "=== 调试cura_inst目录结构 ==="
            if exist "cura_inst" (
                echo "cura_inst directory exists"
                dir cura_inst
                echo "Checking for conanrun.bat in build\generators:"
                if exist "cura_inst\build\generators\conanrun.bat" (
                    echo "conanrun.bat found in build\generators"
                    type cura_inst\build\generators\conanrun.bat
                ) else (
                    echo "ERROR: conanrun.bat not found in build\generators!"
                    echo "Available files in cura_inst:"
                    dir cura_inst /s
                    exit /b 1
                )
            ) else (
                echo "ERROR: cura_inst directory not found!"
                exit /b 1
            )

            echo "=== 激活虚拟环境 ==="
            echo "First calling conanrun.bat:"
            # conanrun.bat: 设置Conan依赖包的环境变量
            call cura_inst\build\generators\conanrun.bat
            if %ERRORLEVEL% neq 0 (
                echo "conanrun.bat failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "Then calling virtual_python_env.bat:"
            # virtual_python_env.bat: 激活Python虚拟环境
            call cura_inst\build\generators\virtual_python_env.bat
            if %ERRORLEVEL% neq 0 (
                echo "virtual_python_env.bat failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "=== 验证Python环境配置 ==="
            python --version
            pip --version
            echo "Python path: %PYTHON%"
            echo "Current PATH: %PATH%"
            echo "PYTHONPATH: %PYTHONPATH%"

            echo "=== 检查Python环境设置 ==="
            echo "Current working directory: %CD%"
            echo "PYTHONPATH: %PYTHONPATH%"

            echo "=== 查找cura模块文件系统位置 ==="
            if exist "_cura_sources\cura" (
                echo "SUCCESS: cura directory found in _cura_sources"
            ) else (
                echo "ERROR: cura directory not found in _cura_sources"
                exit /b 1
            )

            echo "=== Adding _cura_sources to PYTHONPATH ==="
            set PYTHONPATH=%CD%\_cura_sources;%PYTHONPATH%
            echo "Updated PYTHONPATH: %PYTHONPATH%"

            echo "=== Checking for pip requirements files ==="
            if exist "cura_inst\build\generators" (
                echo "generators directory exists"
                dir cura_inst\build\generators
            ) else (
                echo "ERROR: generators directory not found!"
                echo "Contents of cura_inst:"
                dir cura_inst /s
                exit /b 1
            )

            echo "=== Installing additional requirements in Conan environment ==="
            if exist "cura_inst\build\generators\pip_requirements_installer_basic.txt" (
                echo "Installing installer requirements (pyinstaller etc.)..."
                python -m pip install -r cura_inst\build\generators\pip_requirements_installer_basic.txt --no-warn-script-location
            ) else (
                echo "WARNING: pip_requirements_installer_basic.txt not found"
            )

            echo "=== Verifying cura module after pip installs ==="
            echo "Cura module should be available via PYTHONPATH"

            echo "=== Running prepare_installer.py ==="
            echo "Current working directory: %CD%"
            echo "Checking if prepare_installer.py exists:"
            if exist "Cura-workflows\runner_scripts\prepare_installer.py" (
                echo "prepare_installer.py found"
            ) else (
                echo "ERROR: prepare_installer.py not found"
                exit /b 1
            )

            echo "Final cura module test before running prepare_installer.py:"
            python -c "import cura; print('SUCCESS: cura module ready for prepare_installer.py')"
            if %ERRORLEVEL% neq 0 (
                echo "ERROR: cura module not available for prepare_installer.py"
                exit /b %ERRORLEVEL%
            )

            echo "Running prepare_installer.py with arguments:"
            echo "  --os ${{ runner.os }}"
            echo "  --architecture X64"
            echo "  --summary-output %GITHUB_STEP_SUMMARY%"
            echo "  --variables-output %GITHUB_OUTPUT%"

            python Cura-workflows\runner_scripts\prepare_installer.py --os ${{ runner.os }} --architecture X64 ${{ inputs.enterprise && '--enterprise' || '' }} ${{ inputs.private_data && '--internal' || '' }} --summary-output %GITHUB_STEP_SUMMARY% --variables-output %GITHUB_OUTPUT%
            if %ERRORLEVEL% neq 0 (
                echo "prepare_installer.py failed with error level %ERRORLEVEL%"
                exit /b %ERRORLEVEL%
            )

            echo "prepare_installer.py completed successfully"

            echo "=== Checking for pyinstaller spec file ==="
            echo "Contents of cura_inst after deploy:"
            dir cura_inst
            if exist "cura_inst\UltiMaker-Cura.spec" (
                echo "UltiMaker-Cura.spec found, running pyinstaller..."
                pyinstaller cura_inst\UltiMaker-Cura.spec
            ) else (
                echo "ERROR: UltiMaker-Cura.spec not found"
                echo "Checking if deploy() method was called..."
                if exist "cura_inst\packaging" (
                    echo "packaging directory found - deploy() was called"
                ) else (
                    echo "packaging directory not found - deploy() was NOT called"
                )
                exit /b 1
            )

      # 步骤5：复制预编译的CuraEngine.exe
      # 功能：将预编译的CuraEngine.exe复制到分发目录
      # 原因：避免在CI环境中重新编译CuraEngine，节省时间和资源
      - name: Copy precompiled CuraEngine.exe
        run: |
            Write-Host "=== 检查预编译的CuraEngine.exe ==="
            Write-Host "Current directory: $(Get-Location)"
            Write-Host "Contents of current directory:"
            Get-ChildItem . | Select-Object Name, Length | Format-Table -AutoSize
            Write-Host "Contents of _cura_sources:"
            if (Test-Path "_cura_sources") {
                Get-ChildItem "_cura_sources" | Select-Object Name, Length | Format-Table -AutoSize
            } else {
                Write-Host "_cura_sources directory not found"
            }
            Write-Host "Contents of dist:"
            if (Test-Path "dist") {
                Get-ChildItem "dist" -Recurse | Select-Object FullName | Format-Table -AutoSize
            } else {
                Write-Host "dist directory not found"
            }

            # 检查预编译的CuraEngine.exe是否存在
            if (Test-Path "_cura_sources\CuraEngine.exe") {
                Write-Host "Found precompiled CuraEngine.exe, copying to distribution"
                if (Test-Path "dist\UltiMaker-Cura") {
                    # 复制CuraEngine.exe到最终分发目录
                    Copy-Item "_cura_sources\CuraEngine.exe" "dist\UltiMaker-Cura\CuraEngine.exe" -Force
                    Write-Host "CuraEngine.exe copied successfully to dist\UltiMaker-Cura\"
                } else {
                    Write-Error "dist\UltiMaker-Cura directory not found!"
                    exit 1
                }
            } else {
                Write-Error "CuraEngine.exe not found in _cura_sources directory. Please place the precompiled CuraEngine.exe file in the Cura project root."
                exit 1
            }

      # - name: Sign the internal executables (skipped for personal development)
        # working-directory: dist/UltiMaker-Cura
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "CuraEngine.exe"
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "UltiMaker-Cura.exe"
        # timeout-minutes: 2

      # 步骤6：解决MSVC运行时库依赖问题
      # 功能：自动查找并复制Visual Studio C++运行时库到分发目录
      # 原因：确保Cura在没有安装Visual Studio的系统上能正常运行
      - name: Workaround (need exact version of msvc redistributables)
        run: |
          # 尝试不同版本的Visual Studio安装路径
          # 支持企业版、专业版、社区版
          $VS_PATHS = @(
            "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Redist/MSVC",
            "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Redist/MSVC",
            "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Redist/MSVC"
          )

          # 查找可用的Visual Studio安装
          $MSDIR = $null
          foreach ($path in $VS_PATHS) {
            if (Test-Path $path) {
              Write-Host "Found Visual Studio path: $path"
              $MSDIR = $path
              break
            }
          }

          if (-not $MSDIR) {
            throw "Could not find any Visual Studio 2022 installation"
          }

          # 列出所有可用的MSVC版本（用于调试）
          Write-Host "Available MSVC versions in ${MSDIR}:"
          $versions = dir -Exclude "v*" $MSDIR -ErrorAction SilentlyContinue
          foreach ($version in $versions) {
            Write-Host "  - $($version.Name)"
          }

          # 选择最新版本（版本号最高的）
          $MSDIR_R = ($versions | Sort-Object Name -Descending)[0].FullName
          Write-Host "Selected MSVC version: $MSDIR_R"

          # 查找C++运行时库目录
          $CRT_DIRS = dir "$MSDIR_R/x64/Microsoft.VC*.CRT" -ErrorAction SilentlyContinue
          if (-not $CRT_DIRS) {
            throw "Could not find Microsoft.VC*.CRT directory in $MSDIR_R/x64/"
          }

          $MSDIR_DLLS = $CRT_DIRS[0].FullName
          Write-Host "Using CRT directory: $MSDIR_DLLS"

          # 列出可用的DLL文件（用于调试）
          Write-Host "Available DLLs in ${MSDIR_DLLS}:"
          dir $MSDIR_DLLS/*.dll | ForEach-Object { Write-Host "  - $($_.Name)" }

          # 复制DLL文件（带错误检查）
          $dlls = @("concrt140.dll", "msvcp140.dll", "msvcp140_1.dll", "msvcp140_2.dll", "vcruntime140.dll", "vcruntime140_1.dll")
          foreach ($dll in $dlls) {
            $source = "$MSDIR_DLLS/$dll"
            if (Test-Path $source) {
              Write-Host "Copying $dll"
              copy $source dist/UltiMaker-Cura/.
            } else {
              Write-Warning "DLL not found: $source"
            }
          }

      # 步骤7：解决Python库链接问题
      # 功能：将Python库复制到分发目录
      # 原因：避免在CI环境中重新编译Python库，节省时间和资源
      - name: Workaround (some libs linking against python3 instead of python312)
        run: |
          copy Cura-workflows/python_dll_workaround/* dist/UltiMaker-Cura/.

      - name: Yet another step deleting unwanted additional Qt files and folders
        working-directory: dist/UltiMaker-Cura
        run: |
         Remove-Item .\* -Include "*assimp*" -Recurse -Force
         Remove-Item .\* -Include "*qt6charts*" -Recurse -Force
         Remove-Item .\* -Include "*qt6coap*" -Recurse -Force
         Remove-Item .\* -Include "*qt6datavis*" -Recurse -Force
         Remove-Item .\* -Include "*qt6labsani*" -Recurse -Force
         Remove-Item .\* -Include "*qt6mqtt*" -Recurse -Force
         Remove-Item .\* -Include "*qt6networkauth*" -Recurse -Force
         Remove-Item .\* -Include "*quick3d*" -Recurse -Force
         Remove-Item .\* -Include "*qt6timeline*" -Recurse -Force
         Remove-Item .\* -Include "*qt6virtualkey*" -Recurse -Force
         Remove-Item .\* -Include "*waylandcomp*" -Recurse -Force
         Remove-Item .\* -Include "*qt5compat*" -Recurse -Force

      - name: Create the Windows exe installer (Powershell)
        run: |
          $pydir = type ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\NSIS\create_windows_installer.py --source_path ../cura_inst --dist_path . --filename "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # - name: Sign the Windows exe installer (skipped for personal development)
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe"
        # working-directory: dist
        # timeout-minutes: 2

      - name: Upload the installer exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-exe
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe
          retention-days: 5

      - name: Create the Windows msi installer (Powershell)
        run: |
          $pydir = type ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\msi\create_windows_msi.py --source_path ..\cura_inst --dist_path .\UltiMaker-Cura --filename "${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}.msi" --name "${{ steps.prepare-distribution.outputs.CURA_APP_NAME }}" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # - name: Sign the Windows msi installer (skipped for personal development)
        # run: |
        #   & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi"
        # working-directory: dist
        # timeout-minutes: 2

      - name: Upload the installer msi
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-msi
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi
          retention-days: 5

      - name: Upload the application exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: UltiMaker-Cura.exe
          path: dist/UltiMaker-Cura/UltiMaker-Cura.exe
          retention-days: 5

      - name: Upload the engine exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: CuraEngine.exe
          path: dist/UltiMaker-Cura/CuraEngine.exe
          retention-days: 5

      - name: Upload the built Package(s)
        if: ${{ always() }}
        uses: ultimaker/cura-workflows/.github/actions/upload-conan-package@main
        continue-on-error: true

      - name: Clean local cache
        if: ${{ always() && startsWith(inputs.operating_system, 'self-hosted') }}
        run: |
            $pydir = type pydir.txt
            $env:PATH = "$pydir;$pydir\Scripts;" + $env:PATH
            conan remove '*' --lru=1w -c

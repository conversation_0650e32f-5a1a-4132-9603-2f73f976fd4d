name: Process Pull Request

on:
  workflow_call:
    inputs:
      labels:
        required: false
        default: "PR: Community Contribution :crown:"
        type: string

permissions:
  contents: read
  pull-requests: write

jobs:
  add_label:
    runs-on: ubuntu-latest
    steps:
      - uses: actions-ecosystem/action-add-labels@v1
        if: ${{ github.event.pull_request.head.repo.full_name != github.repository }}
        with:
          labels: ${{ inputs.labels }}

# composite action, not to be run on its own, but included in a build script

name: 'Upload conan packages'
description: 'Uploads one or multiple conan packages, to either the public or private repository'

inputs:
  package_name:
    description: 'Package name to upload'
    required: false
    default: "*"


runs:
  using: "composite"
  steps:
      - name: Upload the Package(s)
        shell: bash
        run: |
          if [[ -f pydir.txt ]]; then
            pydir=$(cat pydir.txt)
            PATH+=":$pydir:$pydir/Scripts"
          fi

          # Use private development upload script that skips actual uploads
          if [[ -f runner_scripts/upload_conan_package_private.py ]]; then
            echo "Using private development upload script"
            python runner_scripts/upload_conan_package_private.py "${{ inputs.package_name }}"
          else
            echo "Skipping Conan package upload for private development"
            echo "Would upload package: ${{ inputs.package_name }}"
            # For private development, we skip the actual upload to avoid authentication issues
            # python Cura-workflows/runner_scripts/upload_conan_package.py "${{ inputs.package_name }}"
          fi

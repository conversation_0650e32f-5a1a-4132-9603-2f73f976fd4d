# composite action, not to be run on its own, but included in a build script

name: 'Set package overrides'
description: 'Forces conan packages overrides based on a manually given list of packages versions'

inputs:
  package_overrides:
    required: true
    type: string

  profile:
    default: default
    required: false
    type: string

outputs:
  profile:
    description: "Name of the overridden profile to be used for next steps"
    value: ${{ steps.set-overrides.outputs.profile_override }}

runs:
  using: "composite"
  steps:
      - name: Set the conan packages overrides
        id: set-overrides
        shell: bash
        run: |
          if [[ -f pydir.txt ]]; then
            pydir=$(cat pydir.txt)
            PATH+=":$pydir:$pydir/Scripts"
          fi

          profile_path=$(conan profile path "${{ inputs.profile }}")

          profile_override_suffix=.override
          profile_path_override=$profile_path$profile_override_suffix
          cp -r "$profile_path" "$profile_path_override"

          echo "[replace_requires]" >> "$profile_path_override"
          for package_version in ${{ inputs.package_overrides }}; do
            package_name=$(echo "$package_version" | cut -d'/' -f1)
            echo $package_name/*@*/*: $package_version >> "$profile_path_override"
          done

          echo "profile_override=${{ inputs.profile }}$profile_override_suffix" >> $GITHUB_OUTPUT

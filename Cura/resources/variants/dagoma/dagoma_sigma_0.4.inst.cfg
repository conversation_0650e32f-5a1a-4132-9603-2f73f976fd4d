[general]
definition = dagoma_sigma
name = 0.4mm
version = 4

[metadata]
hardware_type = nozzle
setting_version = 25
type = variant

[values]
acceleration_enabled = True
acceleration_infill = 3000
acceleration_print = 1500
acceleration_travel = 1500
acceleration_travel_layer_0 = 2000
acceleration_wall_0 = 850
acceleration_wall_x = 1600
bridge_fan_speed_2 = 100.0
bridge_fan_speed_3 = 100.0
bridge_settings_enabled = True
bridge_skin_density = 95.0
bridge_skin_density_2 = 85.0
bridge_skin_density_3 = 95.0
bridge_wall_min_length = 2.0
bridge_wall_speed = 25.0
carve_multiple_volumes = True
coasting_enable = True
cool_fan_full_at_height = 0.6
cool_min_layer_time = 6.0
cool_min_speed = 5.0
infill_overlap = 2.0
infill_sparse_density = 15
infill_sparse_thickness = 0.4
infill_wall_line_count = 1
layer_height_0 = 0.25
line_width = 0.4
machine_nozzle_id = 0.4mm
machine_nozzle_size = 0.4
retract_at_layer_change = True
retraction_combing = noskin
retraction_combing_max_distance = 5.0
retraction_count_max = 25
retraction_extrusion_window = 1.0
retraction_hop = 0.0
retraction_hop_enabled = True
retraction_speed = 45
roofing_layer_count = 1
roofing_line_width = 0.3
roofing_material_flow = 95
roofing_monotonic = True
skin_outline_count = 2
skirt_gap = 2.0
small_hole_max_size = 4.0
speed_infill = 60
speed_layer_0 = 14
speed_travel = 140
speed_travel_layer_0 = 27
support_angle = 50.0
support_fan_enable = True
support_infill_rate = =15 if support_enable and support_structure == 'normal' else 0 if support_enable and support_structure == 'tree' else 15
support_infill_sparse_thickness = =resolveOrValue('layer_height')
support_interface_enable = False
support_interface_height = 1.0
support_interface_pattern = zigzag
support_structure = tree
support_top_distance = 0.1
support_type = buildplate
support_xy_distance = 1.5
support_xy_distance_overhang = =machine_nozzle_size / 2
support_z_distance = 0.2
top_bottom_thickness = 0.8
top_thickness = 0.8
travel_avoid_distance = 3.0
travel_avoid_supports = True


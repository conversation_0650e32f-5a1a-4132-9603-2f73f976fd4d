1. 克隆项目
```bash
git clone https://github.com/Ultimaker/Cura.git
git clone https://github.com/Ultimaker/Uranium.git
git clone https://github.com/Ultimaker/CuraEngine.git
```
2. 创建虚拟环境
```bash
python -m venv cura_venv
# On Linux/MacOS
source cura_venv/bin/activate
# On Windows
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
.\cura_venv\Scripts\activate.ps1
```
3. 安装conan并下载conan-config
```bash
pip install conan==2.7.0
conan config install https://github.com/wsd07/conan-config.git
conan profile detect --force
```
4. 设置CuraEngine和Uranium为可编辑模式
```bash
# In CuraEngine
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing
# In Uranium
cd Uranium
conan editable add . --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
```
5. 安装依赖
```bash
pip install gitpython 
cd Cura
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv
```
6. 运行项目
```bash
python cura_app.py
```
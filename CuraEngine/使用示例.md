# CuraEngine 构建脚本使用示例

## 🚀 基本用法

### 1. 完整构建和部署（首次使用）
```cmd
build_curaengine.bat
```
**用途**: 首次构建或依赖项变更后使用
**耗时**: 约5-10分钟
**包含**: 依赖项安装 + 构建 + 部署 + Git提交

### 2. 快速构建（日常开发推荐）
```cmd
build_curaengine.bat --quick
```
**用途**: 代码修改后快速构建
**耗时**: 约1-2分钟
**包含**: 构建 + 部署 + Git提交

### 3. 仅清理构建目录
```cmd
build_curaengine.bat --clean
```
**用途**: 清理构建文件，释放磁盘空间
**耗时**: 约1秒

### 4. 拉取最新源码
```cmd
build_curaengine.bat --pull
```
**用途**: 从GitHub拉取最新的CuraEngine源码
**耗时**: 约5-10秒
**包含**: Git状态检查 + 拉取更新

### 5. 推送源码更改
```cmd
build_curaengine.bat --push
```
**用途**: 将本地CuraEngine源码更改推送到GitHub
**耗时**: 约10-30秒
**包含**: Git状态检查 + 添加更改 + 提交 + 推送

## 🔧 高级选项

### 4. 快速构建但不部署
```cmd
build_curaengine.bat --quick --no-deploy
```
**用途**: 仅测试构建是否成功，不更新Cura

### 5. 构建但不提交Git
```cmd
build_curaengine.bat --no-git
```
**用途**: 构建和部署，但不自动提交到GitHub

### 6. 仅构建测试（不部署不提交）
```cmd
build_curaengine.bat --quick --no-deploy --no-git
```
**用途**: 快速验证代码是否能正确编译

## 📋 选项组合示例

### 开发场景示例

#### 场景1: 修改了源代码，想快速测试
```cmd
build_curaengine.bat -q -nd -ng
```
- `-q`: 快速构建
- `-nd`: 不部署到Cura
- `-ng`: 不提交Git

#### 场景2: 代码修改完成，准备部署
```cmd
build_curaengine.bat --quick
```
- 快速构建并完整部署

#### 场景3: 更新了依赖项配置
```cmd
build_curaengine.bat
```
- 完整重新构建

#### 场景4: 磁盘空间不足
```cmd
build_curaengine.bat --clean
```
- 清理构建文件

## 🎯 推荐工作流程

### 日常开发流程
1. **修改代码** → 编辑 `src/` 目录下的文件
2. **快速测试** → `build_curaengine.bat -q -nd -ng`
3. **验证通过** → `build_curaengine.bat --quick`
4. **在Cura中测试** → 启动Cura验证功能

### 发布准备流程
1. **完整构建** → `build_curaengine.bat`
2. **功能测试** → 在Cura中全面测试
3. **检查GitHub** → 确认提交已推送

## ⏱️ 性能对比

| 命令 | 耗时 | 依赖项 | CMake | 构建 | 部署 | Git |
|------|------|--------|-------|------|------|-----|
| `build_curaengine.bat` | 5-10分钟 | ✅ | ✅ | ✅ | ✅ | ✅ |
| `build_curaengine.bat --quick` | 1-2分钟 | ❌ | 智能 | ✅ | ✅ | ✅ |
| `build_curaengine.bat -q -nd -ng` | 30秒-1分钟 | ❌ | 智能 | ✅ | ❌ | ❌ |
| `build_curaengine.bat --clean` | 1秒 | ❌ | ❌ | ❌ | ❌ | ❌ |

## 🔍 输出信息解读

### 成功指示器
- ✅ "成功: CuraEngine.exe工作正常"
- ✅ "成功: CuraEngine.exe已复制到 Cura目录"
- ✅ "成功: 更改已推送到GitHub仓库"
- ✅ "所有操作已成功完成！"

### 警告信息
- ⚠️ "警告: CuraEngine.exe可能无法正常工作"
- ⚠️ "警告: Git推送失败"

### 错误信息
- ❌ "错误: 请在CuraEngine根目录运行此脚本"
- ❌ "错误: Conan依赖项安装失败"
- ❌ "错误: 构建失败"

## 💡 使用技巧

### 1. 提升构建速度
- 使用SSD硬盘
- 关闭实时杀毒扫描
- 使用快速构建模式

### 2. 节省时间
- 代码修改后优先使用 `--quick`
- 仅在依赖项变更时使用完整构建
- 使用 `--no-deploy --no-git` 进行快速验证

### 3. 故障排除
- 构建失败时先尝试 `--clean` 然后完整构建
- Git推送失败时检查网络和凭据
- 首次使用必须运行完整构建

## 📞 获取帮助

```cmd
build_curaengine.bat --help
```

显示所有可用选项和使用说明。
